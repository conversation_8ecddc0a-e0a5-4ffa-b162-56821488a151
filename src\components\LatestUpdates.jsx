import React from 'react'
import { Calendar, ArrowRight, User } from 'lucide-react'

const LatestUpdates = () => {
  const updates = [
    {
      id: 1,
      title: 'Revolutionary AI Model for Humanoid Navigation',
      excerpt: 'Our latest breakthrough in spatial intelligence allows humanoid robots to navigate complex environments with unprecedented accuracy and safety.',
      date: '2024-01-15',
      author: 'Dr<PERSON>',
      category: 'AI Research',
      image: '🧠',
      readTime: '5 min read'
    },
    {
      id: 2,
      title: 'Partnership with Leading Healthcare Institutions',
      excerpt: 'xSpecies AI announces strategic partnerships to develop healthcare assistant robots for elderly care and medical support.',
      date: '2024-01-10',
      author: '<PERSON><PERSON>',
      category: 'Partnerships',
      image: '🏥',
      readTime: '3 min read'
    },
    {
      id: 3,
      title: 'Open Source Initiative: RoboOS Platform',
      excerpt: 'We\'re releasing our core robotics operating system as open source to accelerate innovation in the robotics community.',
      date: '2024-01-05',
      author: 'Team xSpecies',
      category: 'Open Source',
      image: '💻',
      readTime: '7 min read'
    },
    {
      id: 4,
      title: 'Successful Series A Funding Round',
      excerpt: 'Raised $50M to accelerate development of general-purpose humanoid robots and expand our research team.',
      date: '2023-12-20',
      author: 'Founder Team',
      category: 'Company News',
      image: '💰',
      readTime: '4 min read'
    }
  ]

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <section id="updates" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Latest Updates
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay updated with our latest breakthroughs, partnerships, and innovations
            in the world of AI and robotics.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {updates.map((update, index) => (
            <article key={update.id} className="group cursor-pointer">
              <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-8 h-full hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-blue-200">
                <div className="flex items-center justify-between mb-4">
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">
                    {update.category}
                  </span>
                  <div className="text-3xl">{update.image}</div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors">
                  {update.title}
                </h3>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  {update.excerpt}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{update.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(update.date)}</span>
                    </div>
                  </div>
                  <span className="text-indigo-600 font-medium">{update.readTime}</span>
                </div>

                <div className="mt-6 flex items-center text-indigo-600 font-medium group-hover:text-indigo-700">
                  <span>Read More</span>
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Subscription */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white text-center">
          <h3 className="text-3xl font-bold mb-4">
            Stay in the Loop
          </h3>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and be the first to know about our latest
            innovations, research breakthroughs, and product updates.
          </p>

          <div className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
              <button className="px-6 py-3 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200">
                Subscribe
              </button>
            </div>
          </div>

          <p className="text-sm text-blue-200 mt-4">
            No spam, unsubscribe at any time.
          </p>
        </div>
      </div>
    </section>
  )
}

export default LatestUpdates
