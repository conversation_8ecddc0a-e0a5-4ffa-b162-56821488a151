import React from 'react'
import { Calendar, ArrowRight, User } from 'lucide-react'

const LatestUpdates = () => {
  const updates = [
    {
      id: 1,
      title: 'Why the Future of Robotics Is Not Just Industrial—It\'s Personal',
      excerpt: 'For decades, robots have been confined to factory floors. But the next frontier lies in our homes, hospitals, offices, and communities...',
      date: '2024-01-15',
      author: 'xSpecies Team',
      category: 'Research',
      image: '/ai-3.webp',
      readTime: '5 min read'
    },
    {
      id: 2,
      title: 'Building Intelligence into Movement: The Species AI Approach',
      excerpt: 'One of the hardest problems in robotics is manipulation. At Species AI, we\'re solving this with AIWare—our unified AI platform...',
      date: '2024-01-10',
      author: 'xSpecies Team',
      category: 'Technology',
      image: '/manav.webp',
      readTime: '4 min read'
    },
    {
      id: 3,
      title: 'Why India Needs Its Own Humanoid Robot',
      excerpt: 'India is on the cusp of a demographic transformation. By 2050, over 140 million Indians will be over the age of 60...',
      date: '2024-01-05',
      author: 'xSpecies Team',
      category: 'Industry',
      image: '/Species22.webp',
      readTime: '6 min read'
    }
  ]

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <section id="updates" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Latest Updates
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay updated with our latest breakthroughs, partnerships, and innovations
            in the world of AI and robotics.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {updates.map((update, index) => (
            <article key={update.id} className="group cursor-pointer">
              <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-indigo-200">
                <div className="h-48 overflow-hidden">
                  <img
                    src={update.image}
                    alt={update.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="px-3 py-1 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">
                      {update.category}
                    </span>
                    <span className="text-indigo-600 font-medium text-sm">{update.readTime}</span>
                  </div>

                  <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors line-clamp-2">
                    {update.title}
                  </h3>

                  <p className="text-gray-600 mb-4 leading-relaxed text-sm line-clamp-3">
                    {update.excerpt}
                  </p>

                  <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <User className="w-3 h-3" />
                        <span>{update.author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(update.date)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center text-indigo-600 font-medium group-hover:text-indigo-700 text-sm">
                    <span>Read More</span>
                    <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter Subscription */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white text-center">
          <h3 className="text-3xl font-bold mb-4">
            Stay in the Loop
          </h3>
          <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and be the first to know about our latest
            innovations, research breakthroughs, and product updates.
          </p>

          <div className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
              <button className="px-6 py-3 bg-white text-indigo-600 font-semibold rounded-xl hover:bg-gray-100 transition-colors duration-200">
                Subscribe
              </button>
            </div>
          </div>

          <p className="text-sm text-indigo-200 mt-4">
            No spam, unsubscribe at any time.
          </p>
        </div>
      </div>
    </section>
  )
}

export default LatestUpdates
