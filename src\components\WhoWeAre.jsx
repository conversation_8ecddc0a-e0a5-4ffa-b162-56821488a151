import React from 'react'
import { Brain, <PERSON><PERSON>, Zap, Users } from 'lucide-react'

const WhoWeAre = () => {
  const features = [
    {
      icon: Brain,
      title: 'AI Research',
      description: 'Cutting-edge artificial intelligence research focused on general purpose robotics and human-robot interaction.'
    },
    {
      icon: Cpu,
      title: 'Hardware Innovation',
      description: 'Developing advanced hardware systems that bridge the gap between AI software and physical robotics.'
    },
    {
      icon: Zap,
      title: 'Real-time Processing',
      description: 'Ultra-fast processing capabilities enabling real-time decision making in dynamic environments.'
    },
    {
      icon: Users,
      title: 'Human-Centric Design',
      description: 'Designing robots that understand and adapt to human needs, making technology more accessible.'
    }
  ]

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Who We Are
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            xSpecies AI is at the forefront of robotics innovation, developing intelligent systems
            that seamlessly integrate artificial intelligence with physical robotics to create
            the next generation of humanoid companions and assistants.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="group">
              <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8 h-full hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-indigo-200">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-3xl font-bold mb-4">
                Our Mission
              </h3>
              <p className="text-lg text-indigo-100 leading-relaxed mb-6">
                To democratize robotics technology by making advanced AI-powered robots
                accessible, affordable, and beneficial for everyone. We believe in a future
                where humans and robots work together to solve the world's greatest challenges.
              </p>
              <div className="flex flex-wrap gap-4">
                <span className="px-4 py-2 bg-white/20 rounded-full text-sm font-medium">
                  Innovation
                </span>
                <span className="px-4 py-2 bg-white/20 rounded-full text-sm font-medium">
                  Accessibility
                </span>
                <span className="px-4 py-2 bg-white/20 rounded-full text-sm font-medium">
                  Sustainability
                </span>
              </div>
            </div>
            <div className="text-center lg:text-right">
              <div className="w-32 h-32 mx-auto lg:ml-auto lg:mr-0 mb-4 rounded-2xl overflow-hidden shadow-xl">
                <img
                  src="/ai-3.webp"
                  alt="AI Technology"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="text-2xl font-bold mb-2">Made in India</div>
              <div className="text-indigo-200">For the World</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhoWeAre
