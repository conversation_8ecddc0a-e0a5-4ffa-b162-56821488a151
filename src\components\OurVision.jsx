import React from 'react'
import { Rocket, Globe, Shield, Lightbulb } from 'lucide-react'

const OurVision = () => {
  const visionPoints = [
    {
      icon: Rocket,
      title: 'Pioneering Innovation',
      description: 'Leading the charge in next-generation robotics technology that pushes the boundaries of what\'s possible.'
    },
    {
      icon: Globe,
      title: 'Global Impact',
      description: 'Creating solutions that address worldwide challenges in healthcare, education, and daily life assistance.'
    },
    {
      icon: Shield,
      title: 'Ethical AI',
      description: 'Developing AI systems with built-in ethical frameworks that prioritize human safety and wellbeing.'
    },
    {
      icon: Lightbulb,
      title: 'Continuous Learning',
      description: 'Building robots that learn and adapt continuously, becoming more helpful and intelligent over time.'
    }
  ]

  return (
    <section id="vision" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Vision
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
            We envision a future where human capabilities are augmented beyond current mental and physical limits with the aid of AI, ushering in a new era of human progress and prosperity.
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Our mission is to develop a foundational, end-to-end technology stack for general purpose robots that will be manufactured and deployed at scale in commercial applications by 2026 to significantly improve operational productivity and efficiency. This will culminate into the development and launch of our fully autonomous Humanoid for general purpose use in both business as well as consumer applications by the year 2027.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="space-y-6">
              {visionPoints.map((point, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <point.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {point.title}
                    </h4>
                    <p className="text-gray-600">
                      {point.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative">
            <div className="bg-white rounded-3xl p-8 shadow-2xl">
              <div className="text-center mb-6">
                <div className="text-6xl mb-4">🚀</div>
                <h4 className="text-2xl font-bold text-gray-900 mb-2">
                  Our Timeline
                </h4>
                <p className="text-gray-600">
                  Key milestones ahead
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-indigo-50 rounded-xl">
                  <span className="font-medium text-gray-900">Commercial Robots</span>
                  <span className="text-indigo-600 font-bold">2026</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                  <span className="font-medium text-gray-900">Autonomous Humanoid</span>
                  <span className="text-purple-600 font-bold">2027</span>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-indigo-500 rounded-full opacity-60"></div>
            <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full opacity-60"></div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default OurVision
