import React from 'react'
import { Rocket, Globe, Shield, Lightbulb } from 'lucide-react'

const OurVision = () => {
  const visionPoints = [
    {
      icon: Rocket,
      title: 'Pioneering Innovation',
      description: 'Leading the charge in next-generation robotics technology that pushes the boundaries of what\'s possible.'
    },
    {
      icon: Globe,
      title: 'Global Impact',
      description: 'Creating solutions that address worldwide challenges in healthcare, education, and daily life assistance.'
    },
    {
      icon: Shield,
      title: 'Ethical AI',
      description: 'Developing AI systems with built-in ethical frameworks that prioritize human safety and wellbeing.'
    },
    {
      icon: Lightbulb,
      title: 'Continuous Learning',
      description: 'Building robots that learn and adapt continuously, becoming more helpful and intelligent over time.'
    }
  ]

  return (
    <section id="vision" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Vision
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We envision a future where intelligent robots seamlessly integrate into human society,
            enhancing our capabilities and improving quality of life for people around the world.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div>
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              The Future of Human-Robot Collaboration
            </h3>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Our vision extends beyond traditional robotics. We're building a future where
              robots understand human emotions, adapt to individual preferences, and work
              alongside humans as trusted partners in every aspect of life.
            </p>

            <div className="space-y-6">
              {visionPoints.map((point, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <point.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {point.title}
                    </h4>
                    <p className="text-gray-600">
                      {point.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative">
            <div className="bg-white rounded-3xl p-8 shadow-2xl">
              <div className="text-center mb-6">
                <div className="text-6xl mb-4">🚀</div>
                <h4 className="text-2xl font-bold text-gray-900 mb-2">
                  2030 Vision
                </h4>
                <p className="text-gray-600">
                  Our roadmap to the future
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-indigo-50 rounded-xl">
                  <span className="font-medium text-gray-900">Household Robots</span>
                  <span className="text-indigo-600 font-bold">2025</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-purple-50 rounded-xl">
                  <span className="font-medium text-gray-900">Healthcare Assistants</span>
                  <span className="text-purple-600 font-bold">2027</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-violet-50 rounded-xl">
                  <span className="font-medium text-gray-900">Educational Companions</span>
                  <span className="text-violet-600 font-bold">2028</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-fuchsia-50 rounded-xl">
                  <span className="font-medium text-gray-900">Industrial Automation</span>
                  <span className="text-fuchsia-600 font-bold">2030</span>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full opacity-60"></div>
            <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full opacity-60"></div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Join Us in Shaping the Future
            </h3>
            <p className="text-gray-600 mb-6">
              Be part of the robotics revolution. Whether you're a researcher, developer,
              or simply passionate about technology, there's a place for you in our vision.
            </p>
            <button className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
              Get Involved
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default OurVision
