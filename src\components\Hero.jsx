import React from 'react'
import { ArrowRight, Play } from 'lucide-react'

const Hero = () => {
  return (
    <section id="home" className="hero-bg min-h-screen flex items-center pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="fade-in">
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium text-blue-800 border border-white/30">
                🚀 Building the Future of Robotics
              </span>
            </div>

            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Humanize Product
              <br />
              <span className="gradient-text">Platform</span>
            </h1>

            <p className="text-xl text-gray-700 mb-8 leading-relaxed">
              Developing physical AI software and hardware systems for general purpose
              Robots and Humanoids in India for the world.
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center group shadow-lg">
                Enquiry Now
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>

              <button className="px-8 py-4 bg-white/90 backdrop-blur-sm text-indigo-700 font-semibold rounded-xl hover:bg-white hover:text-indigo-800 transition-all duration-300 flex items-center justify-center group border border-indigo-200 shadow-lg">
                <Play className="mr-2 w-5 h-5" />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="mt-12 grid grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">50+</div>
                <div className="text-sm text-gray-600">AI Models</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">100+</div>
                <div className="text-sm text-gray-600">Robots Built</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">24/7</div>
                <div className="text-sm text-gray-600">Support</div>
              </div>
            </div>
          </div>

          {/* Right Content - Robot Image */}
          <div className="relative">
            <div className="floating-animation">
              <div className="relative w-full max-w-lg mx-auto">
                {/* Main Robot Image */}
                <div className="w-96 h-96 mx-auto relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-full opacity-20 blur-3xl"></div>
                  <div className="relative z-10 w-full h-full rounded-3xl overflow-hidden shadow-2xl">
                    <img
                      src="/manav.webp"
                      alt="xSpecies AI Humanoid Robot"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl floating-animation shadow-lg" style={{animationDelay: '1s'}}>
                  AI
                </div>

                <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center text-white text-2xl floating-animation shadow-lg" style={{animationDelay: '2s'}}>
                  ⚡
                </div>

                <div className="absolute top-1/2 -left-8 w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-lg floating-animation shadow-lg" style={{animationDelay: '0.5s'}}>
                  🔬
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
