import React, { Suspense, useRef } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { useGLTF, OrbitControls, Environment } from '@react-three/drei'

function Model() {
  try {
    const { scene } = useGLTF('/robot-model.glb')
    const modelRef = useRef()

    // Auto-rotate the model
    useFrame((state) => {
      if (modelRef.current) {
        modelRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3
      }
    })

    return (
      <primitive
        ref={modelRef}
        object={scene}
        scale={[1.5, 1.5, 1.5]}
        position={[0, -1, 0]}
      />
    )
  } catch (error) {
    console.error('Error loading 3D model:', error)
    return null
  }
}

function LoadingFallback() {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="text-6xl animate-pulse">🤖</div>
    </div>
  )
}

const RobotModel = () => {
  return (
    <div className="w-full h-96 rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-indigo-50 to-purple-50">
      <Suspense fallback={<LoadingFallback />}>
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ background: 'transparent' }}
        >
          <ambientLight intensity={0.5} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <pointLight position={[-10, -10, -5]} intensity={0.5} />

          <Model />

          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={false}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 3}
          />

          <Environment preset="studio" />
        </Canvas>
      </Suspense>
    </div>
  )
}

// Preload the model
useGLTF.preload('/robot-model.glb')

export default RobotModel
