import React, { Suspense, useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { useGLTF, OrbitControls } from '@react-three/drei'

function Model() {
  const modelRef = useRef()
  const [model, setModel] = useState(null)
  const [error, setError] = useState(false)

  useEffect(() => {
    try {
      const { scene } = useGLTF('/robot-model.glb')
      setModel(scene)
    } catch (err) {
      console.error('Error loading 3D model:', err)
      setError(true)
    }
  }, [])

  // Auto-rotate the model
  useFrame((state) => {
    if (modelRef.current) {
      modelRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3
    }
  })

  if (error || !model) {
    return null
  }

  return (
    <primitive
      ref={modelRef}
      object={model}
      scale={[1.2, 1.2, 1.2]}
      position={[0, -0.5, 0]}
    />
  )
}

function LoadingFallback() {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="text-6xl animate-pulse">🤖</div>
    </div>
  )
}

const RobotModel = () => {
  return (
    <div className="w-full h-96 rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-indigo-50 to-purple-50">
      <Suspense fallback={<LoadingFallback />}>
        <Canvas
          camera={{ position: [0, 0, 4], fov: 60 }}
          style={{ background: 'transparent' }}
          gl={{ antialias: true, alpha: true }}
          dpr={[1, 2]}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[5, 5, 5]} intensity={0.8} />
          <pointLight position={[-5, -5, -5]} intensity={0.4} />

          <Model />

          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={false}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 4}
            enableDamping={true}
            dampingFactor={0.05}
          />
        </Canvas>
      </Suspense>
    </div>
  )
}

// Error Boundary Component
class ModelErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    console.error('3D Model Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-100 to-purple-100 rounded-3xl">
          <div className="text-center">
            <div className="text-6xl mb-4">🤖</div>
            <p className="text-gray-600">3D Model Loading...</p>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Wrapped Component with Error Boundary
const RobotModelWithErrorBoundary = () => {
  return (
    <ModelErrorBoundary>
      <RobotModel />
    </ModelErrorBoundary>
  )
}

// Preload the model
try {
  useGLTF.preload('/robot-model.glb')
} catch (error) {
  console.warn('Could not preload 3D model:', error)
}

export default RobotModelWithErrorBoundary
